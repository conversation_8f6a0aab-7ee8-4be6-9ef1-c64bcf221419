Application:
  Environment: "prod"
  Name: "user"
  IsSecureRedis: true

EpifiDb:
  DbType: "CRDB"
  AppName: "user"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 30
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "user"
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 50
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/feature-engineering"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
  S3:
    UsersBucketName: "epifi-prod-users"
    CreditReportsBucketName: "epifi-prod-credit-reports"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.prod-user-service-redis.iqb2bw.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 3
    PoolSize: 80 # this is set to twice the size of default connection pool size assuming instance run on 4 core CPU
    MinIdleConns: 40 # this allows min. number of conn to be readily available
  ClientName: user
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 2500
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 30

OnboardingStageEventPublisher:
  TopicName: "prod-onboarding-stage-update-topic"

Consent:
  Versions:
    FiTnc: 0
    FedTnc: 0
    FiPrivacyPolicy: 0
    FiWealthTnc: 0
    FiP2pInvestmentTnc: 0
    VpaMigration: 0
    SecureUsageTnC: 1
  Urls:
    FiTnc: "https://fi.money/T&C"
    FiTncNonResident: "https://fi.money/tnc/nr"
    FedTnc: "https://www.federalbank.co.in/epifi-tandc#CASA"
    FiPrivacyPolicy: "https://fi.money/privacy"
    FiWealthTnc: "https://fi.money/wealth/TnC"
    #    TODO(vikas): update before go live
    FiP2pInvestmentTnc: "https://fi.money/wealth/TnC"

FeatureReleaseConfig:
  FeatureConstraints:
    - ONBOARDING_ADD_FUNDS_V2_2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 314
          MinIOSVersion: 450
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # internal
    - FEATURE_ENABLE_ORDER_PHYSICAL_CARD_DURING_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 388
          MinIOSVersion: 542
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_ONBOARDING_PRE_ACCOUNT_CREATION_ADD_FUNDS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 418
          MinIOSVersion: 579
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 422
          MinIOSVersion: 546
    - FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 430 # m192-rc2
          MinIOSVersion: 592 # m192-rc2
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # Pay_Experimental
    - FEATURE_SEND_SMS_DATA_WEALTH_BUILDER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 429
          MinIOSVersion: 100000
    - FEATURE_CA_FLOW_WEALTH_BUILDER_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 441
          MinIOSVersion: 603
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100

Onboarding:
  ActorsWhitelistedForPreFunding:
    - "AC3w9TwrnCS5250207"
  ParentNamePrefillFromCKYCRolloutPercentage: 10
  UqudoCountryIdVerificationConfig:
    EnableTamperCheck: true
    ScreenDetectionThreshold: 30
    PrintDetectionThreshold: 30
    PhotoTamperingThreshold: 30
    IdExpiryThreshold: "2160h" # 3 months
    FaceMatchThreshold: 75
  NonResidentCrossValidationConfig:
    NameMatchScoreThreshold: 0.7
    FmPollingRetryConfig:
      Interval: 300 # default to using milliseconds
      MaxAttempts: 5
    CrossValidationDataSources:
      FEATURE_NON_RESIDENT_SA:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_NUMBER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
      FEATURE_NON_RESIDENT_SA:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_NUMBER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
  ConfirmCardMailingAddress:
    EnableConfirmCardMailingAddressV2: true
  HealthConfig:
    - DOB_AND_PAN:
        From: "2024-03-05 23:00:00.000"
        To: "2024-03-06 01:00:00.000"
        HealthStatus: 2
        Message: "Our partner bank is having a scheduled maintenance activity from 5th March 11:00PM to 6th March 01:00AM. Please try again after 01:00AM."
    - DEDUPE_CHECK:
        From: "2023-06-16 00:00:00.000"
        To: "2022-06-16 03:30:00.000"
        HealthStatus: 2
        Message: "Our partner bank is having a scheduled maintenance activity from 16th June 00:00AM to 16th June 03:30AM. Please try again after 03:30AM."
    - EKYC_NAME_DOB_VALIDATION:
        From: "2023-08-08 23:00:00.000"
        To: "2023-08-09 00:00:00.000"
        HealthStatus: 2
        Message: "Our partner bank is having a scheduled maintenance activity from 8th Aug 23:00PM to 9th Aug 12:00AM. Please try again after 12:00AM."
    - KYC_DEDUPE_CHECK:
        From: "2023-06-16 00:00:00.000"
        To: "2022-06-16 03:30:00.000"
        HealthStatus: 2
        Message: "Our partner bank is having a scheduled maintenance activity from 16th June 00:00AM to 16th June 03:30AM. Please try again after 03:30AM."
    - DEVICE_REGISTRATION:
        From: "2023-06-16 00:00:00.000"
        To: "2022-06-16 03:30:00.000"
        HealthStatus: 2
        Message: "Our partner bank is having a scheduled maintenance activity from 16th June 00:00AM to 16th June 03:30AM. Please try again after 03:30AM."
    - PRE_CUSTOMER_CREATION_CHECK:
        From: "2023-06-16 00:00:00.000"
        To: "2022-06-16 03:30:00.000"
        HealthStatus: 2
        Message: "Our partner bank is having a scheduled maintenance activity from 16th June 00:00AM to 16th June 03:30AM. Please try again after 03:30AM."
    - DEBIT_CARD_PIN_SETUP:
        From: "2023-06-16 00:00:00.000"
        To: "2022-06-16 03:30:00.000"
        HealthStatus: 2
        Message: "Our partner bank is having a scheduled maintenance activity from 16th June 00:00AM to 16th June 03:30AM. Please try again after 03:30AM."
    - EKYC:
        From: "2023-08-08 23:00:00.000"
        To: "2023-08-09 00:00:00.000"
        HealthStatus: 2
        Message: "Our partner bank is having a scheduled maintenance activity from 8th Aug 23:00PM to 9th Aug 12:00AM. Please try again after 12:00AM."
  EnableTriggerNROAccountCreation:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  PrefillParentNameFromPassportOCR:
    MinAndroidVersion: 389
    MinIOSVersion: 545
    FallbackToEnableFeature: true
    DisableFeature: false
  FiniteCodeFromAttributionParamsKey: "deep_link_sub5"
  NextActionDecisionCacheConfig:
    IsCacheEnabled: true
    CacheTTL: "2160h" # 3 months
  OnbDetailsCacheConfig:
    OnbDetailsTTLConfig:
      OnbDoneTTL: "30m"
      OnbInProgressTTL: "30m"
    OnbDetailsMinCacheConfig:
      OnbDoneMinTTL: "720h" #30 days
      OnbInProgressMinTTL: "1h"
      ActorToOnbTTL: "1440h" #60 days
    IsCachingEnabled: true
    IsCacheV2Enabled: true
  Flags:
    EnableSaDeclarationstage: false
    EnablePanAadharCheckInPreCustomerCreationCheckStage: true
    IgnoreErrorsInGetDataForCrossValidationManualReview: false
    EnablePermissionStage:
      MinAndroidVersion: 427
      MinIOSVersion: 590
      FallbackToEnableFeature: false
      DisableFeature: false
    UseNewLivenessFlow:
      MinAndroidVersion: 100000
      MinIOSVersion: 100000
      FallbackToEnableFeature: false
      DisableFeature: true
    BlockNrOnboarding: true
    AllowNRIDedupeUsers: true
    EnableGlobalIssuedPassportARNFlow: true
    EnableCkyc: false
    EnableGlobalIssuedPassportVerification:
      MinAndroidVersion: 418
      MinIOSVersion: 579
      FallbackToEnableFeature: true
      DisableFeature: false
    EnableParentNamePrefillFromCKYC: true
    EnableInstalledAppsCheck: true
    EnableSMSParserDataVerificationStage: true
    EnableRiskCheckForNRUser: false
    EnableRiskScreeningForD2H: false
    SkipCountryIdVerification: false
    SkipLocationCheckForNROnboarding: false
    SkipPassportVerification: false
    EnableGNOAOnError: false
    EnableUpdateProfileDetailsStage: true
    EnableSyncOnboarding: true
    EnableSecureUsageGuidelinesConsent: true
    AllowManualReviewUsers: true
    BlockCCUserForPANLinkage: true
    EnableSavingsIntroScreen:
      MinAndroidVersion: 321
      MinIOSVersion: 461
      FallbackToEnableFeature: true
      DisableFeature: false
    EnableSMSParserConsentScreen:
      MinAndroidVersion: 401
      MinIOSVersion: 99999
      FallbackToEnableFeature: true
      DisableFeature: false
      UnsupportedPlatforms: [ 2 ] # 2 is for iOS
    WealthAnalyserFeature:
      MinIOSVersion: 556
      MinAndroidVersion: 401
      FallbackToEnableFeature: true
      DisableFeature: false
    EnableContactPermissionInOnb:
      MinAndroidVersion: 406
      MinIOSVersion: 568
      FallbackToEnableFeature: true
      DisableFeature: false
    PanAutofill:
      PrefetchCreditReportWithoutPan: true
      RefetchCreditReportOnMismatch: true
    DisabledStages:
      FORM16_CHECK: true
      LINKEDIN_VERIFICATION: true
      SHIPPING_ADDRESS_UPDATE: true
      INITIATE_CKYC: true
      CKYC: true

  ABFeatureReleaseConfig:
    FeatureConstraints:
      - FEATURE_ENABLE_CONSENT_SCREEN_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 419
              MinIOSVersion: 583
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - VKYC_NEW_REVIEW_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 332
              MinIOSVersion: 465
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - REFERRAL_SCREEN_DURING_ONBOARDING_V1:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 49
      - ATT_IOS_PERMISSION_PROMPT:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 1
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - AFFLUENT_USER_BONUS_TRANSITION_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - PRIORITIES_VKYC_OVER_ADD_FUNDS:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 241
              MinIOSVersion: 336
          Buckets:
            - ONE:
                Start: 0 # https://epifi.slack.com/archives/C0101A42ZFW/p1730815217499179
                End: 0
      - ONBOARDING_ADD_FUNDS_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 314
              MinIOSVersion: 450
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 49
      - PHONE_NUMBER_AS_REFERRAL_CODE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - SCREENER_CHOICE_PAGE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 311
              MinIOSVersion: 444
          Buckets:
            - ONE:
                Start: 0
                End: 99
  EditEmploymentInScreener:
    MinIOSVersion: 292
    MinAndroidVersion: 209
    FallbackToEnableFeature: false
    DisableFeature: true
  TotalAmountViaOrderAddFunds: true
  MinAndroidVersionForManualBalanceRefreshOnbAddFunds: 99999
  MinIosVersionForManualBalanceRefreshOnbAddFunds: 99999
  PassportVerificationConfig:
    IgnoreVendorError: false
    PassportExpiryThreshold: "2160h" # 3 months
    DetailsConfirmationFeatureConfig:
      MinAndroidVersion: 382
      MinIOSVersion: 541
      FallbackToEnableFeature: false
      DisableFeature: false
  ReferralOfferCodesDuringOnboarding:
    - CODE_1:
        IsEnabled: true
        BeforeAppliedTitle: "<font color='#313234'>FI200: </font><font color='#5d7d4c'>Get up to ₹200</font>"
        AfterAppliedTitle: "<font color='#313234'>\"FI200\" applied</font>"
        BeforeAppliedDesc: "When you add money to your account"
        AfterAppliedDesc: "Get up to ₹200 when you add money"
        BeforeAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-tag.png"
        AfterAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-success-check.png"
        Code: "FI200"
        UnderlyingFiniteCode: "DLFFH5HHJC"
  ReferralOfferCodesABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
  AppScreeningConfig:
    UANCheckConfig:
      CreditScoreThreshold: 650
    EnableChatbotInChoiceScreen: true
    EnableFiLiteEntryPointInChoiceScreen: true

  AddFundsConfig:
    ShowSkipCtaViaQuest: false
    SkipDurationViaQuest: "168h" # 7d
    V2PageMinorVersion: 1
    ShowV2Page: true

  SyncOnboardingSqsPublisher:
    QueueName: "prod-sync-onboarding-queue"

  UNNameCheckMailingAddress:
    FromAddress: "<EMAIL>"
    ToAddress: "<EMAIL>"
    FromName: "epifi-federal-un-namecheck-failures"
    ToName: "epifi-federal-un-namecheck-failures"

  DirectToFiLite:
    Enable: false
    Variant: ""
  OrderPhysicalDebitCardConfig:
    EnableViaQuest: true
    FreePhysicalDCRefereeRewardConfig:
      IsEnabled: true
      ActiveFrom: "2025-02-03T00:00:00+05:30"
      ActiveTill: "2025-03-31T23:59:59+05:30"
      QuestExpVariablePath: "inappreferral/UserApplicableReferralConstructVariant"
      QuestExpVariableValue: "VARIANT_1"
  PanValidateV2FeatureConfig:
    MinIOSVersion: 484
    MinAndroidVersion: 336
    FallbackToEnableFeature: true
    DisableFeature: false
  PanValidateV3FeatureConfig:
    MinIOSVersion: 546
    MinAndroidVersion: 396
    FallbackToEnableFeature: true
    DisableFeature: false
  AWS:
    Region: "ap-south-1"
    S3:
      BucketNames:
        BucketUsers: "epifi-prod-users"
  MinKycMandatoryAddFundConfig:
    IsEnabled: false
    MinimumAmount:
      CurrencyCode: "INR"
      Units: 100
      Nanos: 0
  BlockOnboardingDueToUnlinkedPANAndAadhaar: true

  KYCDedupeRetryCount: 20
  SyncOnboardingInterval: "10m"
  SyncOnboardingCutOff: "240h"
  DedupeAPICacheTimeout: "24h"
  ReferralOfferWidgetsDuringOnboarding:
    - DLFFH5HHJC: # FI200 based
        IsEnabled: true
        LeftIcon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/offer_widget/onb_offer_widget_ref.png"
        OfferText: "<font color ='#313234'>Offer applied! Get flat </font><font color='#5D7D4C'>₹200 </font><font color='#313234'>when you sign up</font>"
        BgColor: "#E7F7DE"
    - REGULAR:
        IsEnabled: false
        LeftIcon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/offer_widget/onb_offer_widget_ref.png"
        OfferText: "<font color ='#313234'>Offer applied! Get cashback when you spend</font>"
        BgColor: "#E7F7DE"
  OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 242
              MinIOSVersion: 337
          Buckets:
            - ONE:
                Start: 0
                End: 49
  AccountSetupMaxStuckDuration: 20m
  OnboardingVelocityConfig:
    QueryRangeDuration: 24h
    Threshold: 3
    BucketExpiry: 48h
    BucketPrecision: 3

  StuckUserNudges:
    TNC_CONSENT:
      - StuckDuration: "5m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "We just met you and this is crazy..."
            Body: "Finish your KYC to open a Federal Bank Savings Account in minutes!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Let's get to know each other"
            Body: "Finish your KYC and set up your Federal Bank Savings Account!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    INITIATE_CKYC:
      - StuckDuration: "15m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Why do we need PAN card details? 💭"
            Body: "Because you are opening a savings account through Fi! Don’t worry, your details are safe with us. Fill them up now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "We're hoping things PAN out between us!"
            Body: "Keep your PAN and Aadhaar details on hand, and you can open a Federal savings account in minutes! Need help? Talk to our Fi Support Team."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 57 # PAN_REMINDER
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_MISMATCH:
      - StuckDuration: "30m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Houston, we no longer have a problem"
            Body: "Update your Fi app so you can resume opening your savings account now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 62 # KYC_VALIDATION_FAILURE
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    NAME_MISMATCH:
      - StuckDuration: "30m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Let's do this shall we?"
            Body: "We couldn't verify your KYC details the last time. Update the app so you can continue opening your savings account ✨"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 62 # KYC_VALIDATION_FAILURE
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    VKYC:
      - StuckDuration: "120m" #2h
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 44
        NotificationTimeRange:
          NotificationStartTime: "08:00"
          NotificationEndTime: "19:00"
      - StuckDuration: "720m" #12h
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "📞 Complete your KYC verification call"
            Body: "Tap to complete the video KYC call to access your account💡Keep your original PAN card ready."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "08:00"
          NotificationEndTime: "19:00"
      - StuckDuration: "1440m" #24h
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 45
        NotificationTimeRange:
          NotificationStartTime: "08:00"
          NotificationEndTime: "19:00"
      - StuckDuration: "2160m" # 36hours
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🚨 90% done! 1 more step left..."
            Body: "Just finish a 3-minute video KYC call to verify yourself and start using Fi. Tap to start the call now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "08:00"
          NotificationEndTime: "19:00"
      - StuckDuration: "2880m" # 48hrs
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Ready for your video call? 🤓 🤙"
            Body: "Complete your account creation by getting on a quick 3 min video KYC call with us."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "08:00"
          NotificationEndTime: "19:00"
    EMPLOYMENT_VERIFICATION:
      - StuckDuration: "5m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Are you ready for your ₹250 reward?😍"
            Body: "Get up to ₹250 once you complete your sign-up on Fi. This will take just 5 minutes. Start now! ➡️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "💳Ready for your cool debit card?"
            Body: "Just complete your sign-up and get offers from Swiggy, Amazon, Myntra, and more. It takes just 5 minutes. Start now! ➡️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "💪Your personal information is safe."
            Body: "Finish your KYC and your savings account will be ready. Your money is insured up to ₹5L."
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
  CCFiliteStuckUserNudges:
    EMPLOYMENT_VERIFICATION:
      - StuckDuration: "15m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Status updated ✅"
            Body: "Your eligibility for credit card has been updated on the Fi app. Tap to view now️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Action pending"
            Body: "Complete your Credit Card application on the Fi app now!"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    VKYC:
      - StuckDuration: "2h"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 77
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 226
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "12h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "📞 Complete your KYC verification call"
            Body: "Tap to complete the video KYC call to get your credit card. \n💡Keep your original PAN ready."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🚨 90% done! 1 more step left..."
            Body: " Just finish a 3-minute video KYC call to verify yourself. Tap to start the call now! "
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Ready for your video call? 🤓 🤙"
            Body: "Complete your account creation by getting on a quick 3 min video KYC call with us."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_AND_PAN:
      - StuckDuration: "2h"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 74
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2h"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 223
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 224
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your credit card is ready for you 💳"
            Body: "Complete your application now on the Fi app & get access to the most rewarding credit card in just 2 min!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "12h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Why do we need PAN card details? 💭"
            Body: "Because its required to process your credit card application! Don’t worry, your details are safe. Fill them up now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "36h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your application is 50% complete!"
            Body: "It wil take just 2 more minutes to access your new Fi-Federal Credit Card. Complete your application now >"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "3% back on every spend 😱"
            Body: "That's what you get with AmpliFi Fi-Federal Credit Card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "96h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "0 Forex & Lounge Access 😱"
            Body: "That's what you get with AmpliFi Fi-Federal Credit Card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EKYC:
      - StuckDuration: "48h"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 75
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2h"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 225
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🪪 Aadhaar, get set, go!"
            Body: "Enter your Aadhaar details to continue with your credit card application on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "You are just 2️⃣ steps away"
            Body: "from getting the most rewarding credit card. Complete your application now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Reminder"
            Body: "Your credit card application is still pending. Complete it now in just 2 minutes on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    LIVENESS:
      - StuckDuration: "24h"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 76
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "30m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "90% Done!"
            Body: "It will take hardly a minute to get your hands on your new credit card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "24h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Action Pending ⚠️"
            Body: "Complete your application for the AmpliFi Fi-Federal Credit Card. You will get the digital card instantly"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Reminder"
            Body: "Your credit card application is still pending. Complete it now in just 2 minutes on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

  StuckUserAlerts:
    FEATURE_NON_RESIDENT:CUSTOMER_CREATION:
      - StuckDuration: "15m"
    FEATURE_SA_UPDATE:PROFILE_DETAILS:
      - StuckDuration: "15m"
    FEATURE_SA:CUSTOMER_CREATION:
      - StuckDuration: "15m"
      # - StuckDuration: "72h"
      # - StuckDuration: "30m"
      # UN_NAME_CHECK:
      # - StuckDuration: "72h"
    FEATURE_SA:PAN_NAME_CHECK:
      - StuckDuration: "6m"
    FEATURE_SA:DEVICE_REGISTRATION:
      - StuckDuration: "10m"
    FEATURE_SA:ACCOUNT_CREATION:
      - StuckDuration: "15m"
      # - StuckDuration: "72h"
      # - StuckDuration: "30m"
    FEATURE_SA:SHIPPING_ADDRESS_UPDATE:
      - StuckDuration: "15m"
      # - StuckDuration: "72h"
      # - StuckDuration: "30m"
    FEATURE_SA:CARD_CREATION:
      - StuckDuration: "15m"
      # - StuckDuration: "72h"
      # - StuckDuration: "30m"
      # CHECK_CREDIT_REPORT_PRESENCE:
      # - StuckDuration: "5m"
      # - StuckDuration: "3h"
    FEATURE_SA:CREDIT_REPORT_VERIFICATION:
      - StuckDuration: "5m"
      - StuckDuration: "3h"
    FEATURE_SA:ADD_MONEY:
      - StuckDuration: "25h"
      - StuckDuration: "73h"
      - StuckDuration: "169h"

  StageManualPassNotif:
    PAN_NAME_CHECK:
      - IsEnabled: true
        NotificationType: "PUSH_NOTIFICATION"
        Title: "30 Lakh Fi users have added Aadhaar"
        Body: "Your PAN verification is complete. Log in to the app and open your Federal savings account now!"
        IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Type: "SYSTEM_TRAY"

  DurationToSkipAddFundsForAffluenceClasses:
    - ENTRY_1:
        IsEnabled: false
        AffluenceClass: 1
        Duration: 24h
    - ENTRY_2:
        IsEnabled: false
        AffluenceClass: 2
        Duration: 72h
    - ENTRY_3:
        IsEnabled: false
        AffluenceClass: 3
        Duration: 168h
  WebUrlsForSalaryB2BFlows:
    - FLOW_1:
        IsEnabled: true
        Url: "https://fi.money/signup"
  KYCNameUpdateNewSubTitleMinAndroid: 79

  BlockOnboardingFromTime: "2025-02-04 00:00:00.000"
  BlockOnboardingTillTime: "2025-02-04 00:30:00.000"
  BlockOnboardingMsg: "Our partner bank is having a scheduled maintenance activity from 4th February 12:00AM to 4th February 12:30AM. Please try again after 12:30AM."
  EKYCCertUpgradeFeatureConfig:
    MinIOSVersion: 1
    MinAndroidVersion: 1
    FallbackToEnableFeature: true
  LivenessSummaryExpiryDuration: "720h" #30 days
  FiLiteRolloutPercentage: 1000
  IntentSelectionConfigV2:
    EnableDefaultIntentSelection: true
    IntentCollectionScreenFeatureConfig:
      MinIOSVersion: 424
      MinAndroidVersion: 299
      FallbackToEnableFeature: true
      DisableFeature: false
    IntentCollectionScreenPercentageRollout: 100
    IntentConfigMap:
      ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT:
        FeatureConfig:
          MinIOSVersion: 424
          MinAndroidVersion: 299
          FallbackToEnableFeature: true
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_PERSONAL_LOANS:
        FeatureConfig:
          MinIOSVersion: 424
          MinAndroidVersion: 299
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_CREDIT_CARD:
        FeatureConfig:
          MinIOSVersion: 452
          MinAndroidVersion: 317
          FallbackToEnableFeature: false
          DisableFeature: true
        RolloutPercentage: 100
      ONBOARDING_INTENT_FI_LITE:
        FeatureConfig:
          MinIOSVersion: 557
          MinAndroidVersion: 401
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_NET_WORTH:
        FeatureConfig:
          MinIOSVersion: 461
          MinAndroidVersion: 321
          FallbackToEnableFeature: false
          DisableFeature: true
        RolloutPercentage: 0
      ONBOARDING_INTENT_WEALTH_ANALYSER:
        FeatureConfig:
          MinIOSVersion: 557
          MinAndroidVersion: 401
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_DEBIT_CARD:
        FeatureConfig:
          MinIOSVersion: 554
          MinAndroidVersion: 399
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100

  AddFundsRestrictionMap:
    ANDROID:
      IsRestricted: false
    IOS:
      IsRestricted: false

  AffluenceClassesEligibleForBonusTransitionScreen:
    - "AFFLUENCE_CLASS_CLASS_1": true
    - "AFFLUENCE_CLASS_CLASS_2": true
    - "AFFLUENCE_CLASS_CLASS_3": false
    - "AFFLUENCE_CLASS_CLASS_4": false
    - "AFFLUENCE_CLASS_CLASS_5": false
  SecureUsageGuidelineVersion: 1
  SecureUsageGuidelineConsentInterval: "1440h" # 60 days (~2 months)
  NrBucketName: "epifi-prod-nrusers"
  IsPanDOBDropOffToWealthAnalyserEnabled: false

Flags:
  TrimDebugMessageFromStatus: false
  EnableActiveProductsByPANCheck: true

NewOnFi:
  Count: 4
  TimeWindow:
    Value: 24
    TimeUnit: "Hour"

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Secrets:
  Ids:
    DbUsernamePassword: "prod/rds/epifimetis/nudge_dev_user"
    PgdbCredentials: "prod/rds/epifimetis/feature-engineering"

OnboardingUserUpdatePublisher:
  TopicName: "prod-user-update-topic"

OnboardingUserUpdateVKYCSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-onboarding-user-update-vkyc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ShippingAddressUpdatePublisher:
  QueueName: "prod-shipping-address-update-queue"

ShippingAddressUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-shipping-address-update-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed hourly for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 8
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 168
          TimeUnit: "Hour"
      MaxAttempts: 176
      CutOff: 8
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "user"

ShippingAddressUpdateCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-shipping-address-update-callback-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "user"

VKYCUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-vkyc-update-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~9 hours post that regular interval is followed hourly for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 60
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 168
          TimeUnit: "Hour"
      MaxAttempts: 178
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "user"

EKYCSuccessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-kyc-ekyc-success-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~42 min post that regular interval is followed for next ~200 minutes
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 20
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 7
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "user"

UserAccessRevokeUpdatePublisher:
  TopicName: "prod-user-access-revoke-update-topic"
ShippingAddressUpdateEventPublisher:
  TopicName: "prod-user-shipping-address-update-topic"

VKYC:
  EnableVKYCFlowV2:
    MinAndroidVersion: 442
    MinIOSVersion: 612
    FallbackToEnableFeature: false
    DisableFeature: false
  EnablePassportFaceImageCheckForNRExpiry: true
  NRKYCExpiryForVKYC: "480h" # 20 days * 24 hours. https://monorail.pointz.in/p/fi-app/issues/detail?id=89435
  Option:
    VKYC_OPTION_LSO:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 0
      MinAndroidVersion: 2000
      MinIOSVersion: 2000
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CKYC_O:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 136
      MinIOSVersion: 225
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_ONBOARDING:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 100
      MinAndroidVersion: 176
      MinIOSVersion: 257
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: true
    VKYC_OPTION_STUDENT:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 198
      MinIOSVersion: 283
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_PARTIAL_KYC_DEDUPE:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 214
      MinIOSVersion: 301
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_EKYC_NUMBER_MISMATCH:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 240
      MinIOSVersion: 335
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_LOW_QUALITY_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 2000
      MinIOSVersion: 2000
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_USERS:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 0
      MinAndroidVersion: 240
      MinIOSVersion: 335
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_CC_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 240
      MinIOSVersion: 335
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CLOSED_ACCOUNT_REOPENING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 299
      MinIOSVersion: 424
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_NON_RESIDENT_ONBOARDING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FEDERAL_LOANS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
  EnableDemandManagement: false
  ShowAuditorAcceptedTileTime: "8h"

ConsentEventPublisher:
  TopicName: "prod-consent-topic"

VpaMigrationConsentPublisher:
  QueueName: "prod-upi-vpa-migration-consent-queue"

# Time should be in HH:MM 24-hour format in IST Time zone
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: true
  StartTime: "11:00"
  EndTime: "12:30"

CreditReportPresencePublisher:
  QueueName: "prod-user-credit-report-presence-queue"

CreditReportPresenceSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-credit-report-presence-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

CreditReportVerificationPublisher:
  QueueName: "prod-user-credit-report-verification-queue"

CreditReportVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-credit-report-verification-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

Screening:
  CreditReportPresenceCheck:
    CreditReportPresenceCheckMaxDuration: 30s
  CreditReportVerification:
    CreditScoreThreshold: 600
    CreditReportVerificationMaxDuration: 30s

UserUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

BankCustomerUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-bank-customer-update-event-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "onboarding"

CreditReportVerificationEventSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-onboarding-credit-report-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "onboarding"

LivManualReviewEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-onboarding-liveness-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "onboarding"

AfPurchasePublisher:
  QueueName: "prod-event-af-purchase-queue"

EventsAfPurchaseSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-event-af-purchase-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed hourly for 1 day.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 8
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 24
          TimeUnit: "Hour"
      MaxAttempts: 32
      CutOff: 8

EventsCompletedTnCPublisher:
  QueueName: "prod-event-completed-tnc-queue"

EventsCompletedTnCSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-event-completed-tnc-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed hourly for 1 day.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 8
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 24
          TimeUnit: "Hour"
      MaxAttempts: 32
      CutOff: 8
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
        Namespace: "user"

UserCacheConfig:
  IsCachingEnabled: true
  UserIdPrefix: "user_id_"
  CacheTTl: "1h"

UserGroupCacheConfig:
  IsCachingEnabled: true
  UserGroupEmailPrefix: "usrGrp:"
  CacheTTl: "10m"

MinimalUserCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "720h" # 30 days

Events:
  AfPurchasePublishDelay: "3h"
  CompletedTnCPublishDelay: "10m"

CreditReportConfig:
  ExperianConsentConfig:
    ConsentExtension: "2160h"
    ConsentExpiry: "4320h"
  CreditReportPresenceEnabled: false
  DownloadWaitConfigForWealthBuilder:
    MaxAttemptsForCheckingDownloadStatus: 6
    SleepDurationBetweenEachAttempt: "500ms"

CreditReportDerivedAttributesPublisher:
  QueueName: "prod-credit-report-derived-attributes-queue"

CreditReportDerivedAttributesSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-credit-report-derived-attributes-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessOnboardingEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-contact-onboarding-stage-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "user"

ProcessAfuEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-contact-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "user"

ProcessDeleteUserSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-contact-delete-user-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 20
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "user"

RecordHashedContactLockTimeout: 10s

UserDevicePropertiesUpdatePublisher:
  TopicName: "prod-user-device-properties-update-topic"

DeleteUserPublisher:
  TopicName: "prod-delete-user-event-topic"

ProcessAccessRevokeCooldownPublisher:
  QueueName: "prod-user-access-revoke-cooldown-queue"

ProcessAccessRevokeCooldownSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-access-revoke-cooldown-queue"
  RetryStrategy:
    RegularInterval:
      BaseInterval: 6
      MaxAttempts: 100
      TimeUnit: "Hour"

# todo: when required make sure additional validations in DAO are added if new values are added here (validity of update based on cooldown duration)
AccessRevokeCooldownDuration:
  ACCESS_REVOKE_REASON_COOLDOWN_UPI_PIN_RETRIES_EXCEEDED_POST_AFU: "24h"
SyncOnboardingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-sync-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 50
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
        Namespace: "onboarding"

VKYCCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
        Namespace: "user"

InHouseVkycCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-user-inhouse-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
        Namespace: "user"

ProcessSavingsAccountUpdateEvent:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-onboarding-savings-account-state-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "onboarding"

ProcessCardCreationEvent:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-onboarding-card-creation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 5
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
        Namespace: "onboarding"

UserDevicePropertiesCacheConfig:
  IsCacheEnabled: false
  CacheTTL: "30m"
