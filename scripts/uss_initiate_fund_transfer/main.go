// nolint:funlen, goimports
package main

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"flag"
	"fmt"
	"strings"
	"time"

	"github.com/epifi/be-common/api/vendorgateway"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	protoJson "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"gorm.io/gorm"

	accountsPb "github.com/epifi/gamma/api/accounts"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/usstocks"
	accountMgPb "github.com/epifi/gamma/api/usstocks/account"
	catalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usStocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/scripts/uss_initiate_fund_transfer/config"
)

var (
	// actor id provided in request must be hardcoded here to avoid misuse of this script
	actorIds = []string{"AC210517rd44ELnTQuGGIKfD6O9TkQ==", "ACUEorasEP33241230", "AC211121czGAe07PSSmy9YLrlNYx2A=="}
)

var (
	requestType            = flag.String("request_type", "", "request type, eg. get bank details, initiate fund transfer, create bank relationship")
	actorId                = flag.String("actor_id", "", "actor id mandatory for all request types")
	vendorAccId            = flag.String("vendor_acc_id", "", "vendor account id mandatory for all request types")
	bankRelationshipId     = flag.String("bank_relationship_id", "", "provide bank relationship id for initiate fund transfer")
	amount                 = flag.Float64("amount", 0, "provide amount in dollars for initiate fund transfer (upto 2 decimal places)")
	bankIdentificationCode = flag.String("bank_identification_code", "", "mandatory for create_bank_relationship")
	fundTransferId         = flag.String("fund_transfer_id", "", "mandatory for get_fund_transfer_details")
	postalAddressString    = flag.String("postal_address", "", "postal address in json format")
	bankAccDetailsString   = flag.String("bank_account_details", "", "bank account details in json format (use api/typesv2/bank_account_details.pb.go)")
	bankAccDetails         *types.BankAccountDetails
	postalAddress          *postaladdress.PostalAddress
	invoiceIds             = flag.String("invoice_ids", "", "comma separated invoice ids")
)

var (
	vgStocksClient       vgStocksPb.StocksClient
	accountManagerClient accountMgPb.AccountManagerClient
	catalogManagerClient catalogPb.CatalogManagerClient
	orderMangerClient    usStocksOrderPb.OrderManagerClient
	userManagerClient    user.UsersClient
	s3Client             *s3.Client
)

const (
	sellAllSymbols                    = "sell_all_symbols"
	getBankDetails                    = "get_bank_details"
	deleteBankRelationship            = "delete_bank_relationship"
	createBankRelationship            = "create_bank_relationship"
	initiateFundTransfer              = "initiate_fund_transfer"
	getFundTransferDetails            = "get_fund_transfer_details"
	getVendorTransferDetailsBatchSize = 20
	getAddressFromInvoices            = "get_address_from_invoices"

	invoiceNumber = "invoice_number"
	postalCode    = "postal_code"
	state         = "state"
)

type WalletOrder struct {
	ActorId          string
	GstInvoiceNumber string
}

type AddressCsvRow struct {
	InvoiceNumber string
	State         string
	PostalCode    string
}

func main() {
	flag.Parse()
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}

	logger.Init(envName)

	defer func() { _ = logger.Log.Sync() }()

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}

	ctx := context.Background()
	if requestType == nil {
		logger.Error(ctx, "requestType is nil")
		panic("request type cannot be nil")
	}

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	vgStocksClient = vgStocksPb.NewStocksClient(vgConn)

	usStocksServiceConn := epifigrpc.NewConnByService(cfg.US_STOCKS_SERVICE)
	defer epifigrpc.CloseConn(usStocksServiceConn)
	accountManagerClient = accountMgPb.NewAccountManagerClient(usStocksServiceConn)
	catalogManagerClient = catalogPb.NewCatalogManagerClient(usStocksServiceConn)
	orderMangerClient = usStocksOrderPb.NewOrderManagerClient(usStocksServiceConn)

	userServiceConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userServiceConn)
	userManagerClient = user.NewUsersClient(userServiceConn)
	// Connect to US stocks Alpaca DB
	alpacaDb, err := storageV2.NewGormDB(conf.USStocksAlpacaDb)
	if err != nil {
		logger.ErrorNoCtx("error while establishing connection to db", zap.Error(err))
		return
	}

	// fetch the s3 client
	awsSession, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		logger.Panic("error creating new aws session")
		return
	}
	s3Client = s3.NewClient(awsSession, conf.Aws.S3.BaseBucketName)

	if *requestType != getAddressFromInvoices {
		err = validateVendorAccountId(ctx, *actorId, *vendorAccId)
		if err != nil {
			logger.Error(ctx, "validation failed for vendor account id", zap.Error(err))
			return
		}
	}

	switch *requestType {
	case sellAllSymbols:
		err = sellAllPositionsForUser(ctx, *actorId, *vendorAccId)
		if err != nil {
			logger.Error(ctx, "failed to sell all positions for user", zap.Error(err))
			return
		}
	case getBankDetails:
		err = getUssBankDetails(ctx, *vendorAccId)
		if err != nil {
			logger.Error(ctx, "failed to get bank relationship details", zap.Error(err))
			return
		}
	case deleteBankRelationship:
		err = validateDetailsForDeleteBankRelationship(*vendorAccId, *bankRelationshipId)
		if err != nil {
			logger.Error(ctx, "validation failed for delete bank relationship", zap.Error(err))
			return
		}
		err = deleteBankRelationshipWithVendor(ctx, *vendorAccId, *bankRelationshipId)
		if err != nil {
			logger.Error(ctx, "failed to delete bank relationship with vendor", zap.Error(err))
			return
		}
		return
	case createBankRelationship:
		postalAddress = &postaladdress.PostalAddress{}
		err = protoJson.Unmarshal([]byte(*postalAddressString), postalAddress)
		if err != nil {
			fmt.Println("error in unmarshalling postal address")
			panic("error in unmarshalling postal address")
		}
		bankAccDetails = &types.BankAccountDetails{}
		err = protoJson.Unmarshal([]byte(*bankAccDetailsString), bankAccDetails)
		if err != nil {
			fmt.Println("error in unmarshalling bank account details")
			panic("error in unmarshalling bank account details")
		}
		err = validateDetailsForCreateBankRelationship()
		if err != nil {
			logger.Error(ctx, "validation failed for create bank relationship", zap.Error(err))
			return
		}
		err = createBankRelationshipWithVendor(ctx, *vendorAccId)
		if err != nil {
			logger.Error(ctx, "failed to create bank relationship with vendor", zap.Error(err))
			return
		}
	case initiateFundTransfer:
		err = validateDetailsForInitiateFundTransfer()
		if err != nil {
			logger.Error(ctx, "validation failed for initiate fund transfer", zap.Error(err))
			return
		}
		err = initiateUssFundTransfer(ctx, *vendorAccId, *bankRelationshipId, *amount)
		if err != nil {
			logger.Error(ctx, "failed to initiate fund transfer", zap.Error(err))
			return
		}
	case getFundTransferDetails:
		if fundTransferId == nil || *fundTransferId == "" {
			logger.Error(ctx, "fund transfer id is nil for get fund transfer details")
			panic("fund transfer id is nil for get fund transfer details")
		}
		err = getUssFundTransferDetail(ctx, *vendorAccId, *fundTransferId)
		if err != nil {
			logger.Error(ctx, "failed to get fund transfer details", zap.Error(err))
			return
		}
	case getAddressFromInvoices:
		if invoiceIds == nil || *invoiceIds == "" {
			logger.Error(ctx, "invoice ids is nil for get address details")
			panic("invoice ids are nil for get address details")
		}
		err := fetchAddressFromInvoices(ctx, alpacaDb, *invoiceIds)
		if err != nil {
			logger.Error(ctx, "failed to get address from invoices", zap.Error(err))
			return
		}
	default:
		logger.Error(ctx, "requestType value is invalid", zap.Any("", *requestType))
		return
	}
}

func sellAllPositionsForUser(ctx context.Context, actorId, vendorAccId string) error {
	// get all open positions for user
	openPosRes, err := vgStocksClient.GetAllOpenPositions(ctx, &vgStocksPb.GetAllOpenPositionsRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		AccountId: vendorAccId,
	})
	if err = epifigrpc.RPCError(openPosRes, err); err != nil {
		logger.Error(ctx, "failed to get all open positions from", zap.String("vendor_acc_id", vendorAccId), zap.Error(err))
		return errors.Wrap(err, "failed to get all open positions from broker")
	}

	openSymbols := lo.Map(openPosRes.GetPositions(), func(position *vgStocksPb.Position, _ int) string {
		return position.GetSymbol()
	})

	// get stock ids from all open symbols
	getStocksRes, err := catalogManagerClient.GetStocks(ctx, &catalogPb.GetStocksRequest{
		Identifiers: &catalogPb.GetStocksRequest_Symbols{Symbols: &catalogPb.RepeatedStrings{Ids: openSymbols}},
	})
	if err = epifigrpc.RPCError(getStocksRes, err); err != nil {
		if getStocksRes.GetStatus().IsRecordNotFound() {
			return errors.Wrap(err, "stock id not found for symbol")
		}
		return errors.Wrap(err, "error getting stock ids for symbols")
	}

	if len(getStocksRes.GetStocks()) != len(openPosRes.GetPositions()) {
		return errors.New("stock details not found for all symbols")
	}

	for _, position := range openPosRes.GetPositions() {
		stockDetails, found := getStocksRes.GetStocks()[position.GetSymbol()]
		if !found {
			logger.Error(ctx, "stock details not found for symbol", zap.Any("symbol", position.GetSymbol()))
			return errors.New("stock details not found for symbol")
		}

		// place sell all order
		createOrderResp, createOrderErr := orderMangerClient.CreateOrder(ctx, &usStocksOrderPb.CreateOrderRequest{
			ClientOrderId: uuid.NewString(),
			ActorId:       actorId,
			CatalogRefId:  stockDetails.GetId(),
			Side:          usstocks.OrderSide_SELL,
			Type:          usstocks.OrderType_ORDER_TYPE_SELL_ALL,
			FundingType:   usstocks.OrderFundingType_ORDER_FUNDING_TYPE_WALLET,
			// this amount will not be used for placing sell order
			// sending this so that validation in backend does not fail
			AmountRequested: &money.Money{CurrencyCode: "USD", Units: 1},
			CalculatedTradeAmountAfterChargesDeduction: &money.Money{CurrencyCode: "USD"},
		})
		if err = epifigrpc.RPCError(createOrderResp, createOrderErr); err != nil {
			logger.Error(ctx, "error placing sell all for symbol", zap.Any("symbol", position.GetSymbol()), zap.Error(err))
			return errors.Wrap(err, "error placing sell all for symbol")
		}
		logger.Info(ctx, "sell order placed successfully", zap.Any("create order response", createOrderResp))
	}
	return nil
}

func getUssBankDetails(ctx context.Context, vendorAccId string) error {
	resp, err := vgStocksClient.GetBankDetails(ctx, &vgStocksPb.GetBankDetailsRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		BrokerAccountId: vendorAccId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "failed to get bank details from broker", zap.String("vendor_acc_id", vendorAccId), zap.Error(err))
		return errors.Wrap(err, "failed to get bank details from broker")
	}
	logger.Info(ctx, fmt.Sprintf("GetBankDetails response: %v", resp))
	return nil
}

func createBankRelationshipWithVendor(ctx context.Context, vendorAccId string) error {
	req := &vgStocksPb.SendBankDetailsRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		BrokerAccountId:        vendorAccId,
		BankAccountDetails:     bankAccDetails,
		BankIdentificationCode: *bankIdentificationCode,
		Address:                postalAddress,
	}

	logger.Info(ctx, fmt.Sprintf("SendBankDetails request: %v", req))
	resp, err := vgStocksClient.SendBankDetails(ctx, req)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "failed to create bank relationship with broker", zap.String("vendor_acc_id", vendorAccId), zap.Error(err))
		return errors.Wrap(err, "failed to create bank relationship with broker")
	}

	logger.Info(ctx, fmt.Sprintf("SendBankDetails response: %v", resp))
	return nil
}

func initiateUssFundTransfer(ctx context.Context, vendorAccId, bankRelationshipId string, amount float64) error {
	amountInUSD := moneyPb.ParseFloat(amount, moneyPb.USDCurrencyCode)

	tradingAccRes, err := vgStocksClient.GetTradingAccount(ctx, &vgStocksPb.GetTradingAccountRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		AccountId: vendorAccId,
	})
	if err = epifigrpc.RPCError(tradingAccRes, err); err != nil {
		logger.Error(ctx, "failed to get trading account details from broker", zap.String("vendor_acc_id", vendorAccId), zap.Error(err))
		return errors.Wrap(err, "failed to get trading account details from broker")
	}
	logger.Info(ctx, fmt.Sprintf("Trading account response: %v", tradingAccRes))

	// doing sanity check on the amount entered by the script runner by fetching the wallet balance.
	// the amount being transferred should be <= wallet balance
	compareWalletBalance, err := moneyPb.CompareV2(tradingAccRes.GetTradingAccount().GetWithdrawableAmount(), amountInUSD)
	if err != nil {
		logger.Error(ctx, "failed to compare wallet balance with amount entered", zap.Error(err))
		return errors.Wrap(err, "failed to compare wallet balance with amount entered")
	}

	if compareWalletBalance == -1 {
		logger.Error(ctx, "amount entered to initiate transfer is greater than wallet balance", zap.Any("wallet_balance", tradingAccRes.GetTradingAccount().GetWithdrawableAmount()), zap.Any("amount", amount))
		return errors.Wrap(err, "amount entered to initiate transfer is greater than wallet balance")
	}

	req := &vgStocksPb.InitiateFundTransferRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		AccountId:          vendorAccId,
		BankRelationshipId: bankRelationshipId,
		Amount:             amountInUSD,
		Direction:          vgStocksPb.FundTransferDirection_FUND_TRANSFER_DIRECTION_OUTGOING,
		TransferMode:       vgStocksPb.FundTransferMode_FUND_TRANSFER_MODE_WIRE,
		FeePaymentMethod:   vgStocksPb.FundTransferFeePaymentMode_FUND_TRANSFER_FEE_PAYMENT_MODE_USER,
	}
	logger.Info(ctx, fmt.Sprintf("InitiateFundTransfer request: %v", req))

	resp, err := vgStocksClient.InitiateFundTransfer(ctx, req)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "failed to initiate funds transfer from broker", zap.String("vendor_acc_id", vendorAccId), zap.String("relationship_id", bankRelationshipId), zap.Error(err))
		return errors.Wrap(err, "failed to initiate funds transfer from broker")
	}

	logger.Info(ctx, fmt.Sprintf("InitiateFundTransfer response: %v", resp))
	return nil
}

func getUssFundTransferDetail(ctx context.Context, vendorAccId, fundTransferId string) error {
	var offset int32
	var transferDetails *vgStocksPb.FundTransfer
	for {
		vendorRes, err := vgStocksClient.GetAllFundTransfers(ctx, &vgStocksPb.GetAllFundTransfersRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_ALPACA,
			},
			Direction: vgStocksPb.FundTransferDirection_FUND_TRANSFER_DIRECTION_OUTGOING,
			AccountId: vendorAccId,
			Offset:    offset,
			Limit:     getVendorTransferDetailsBatchSize,
		})
		if rpcErr := epifigrpc.RPCError(vendorRes, err); rpcErr != nil {
			logger.Error(ctx, "failed to get fund transfer details", zap.String("vendor_acc_id", vendorAccId), zap.Error(err))
			return errors.Wrap(err, "failed to get fund transfer details")
		}
		for _, fundTransfer := range vendorRes.GetFundTransfers() {
			if fundTransfer.GetId() == fundTransferId {
				transferDetails = fundTransfer
				break
			}
		}
		if transferDetails != nil {
			break
		}
		// indicates that we've iterated through all the transfers.
		if len(vendorRes.GetFundTransfers()) < getVendorTransferDetailsBatchSize {
			break
		}

		offset += getVendorTransferDetailsBatchSize
	}
	if transferDetails == nil {
		return errors.New("no fund transfer details found for the given fund transfer id")
	}
	logger.Info(ctx, fmt.Sprintf("Fund Transfer details: %v", transferDetails))
	return nil
}

func validateVendorAccountId(ctx context.Context, actorId, vendorAccId string) error {
	// as vendor account id and actor id is mandatory for all the request types, checking it here
	if vendorAccId == "" {
		return errors.New("vendor account id cannot be empty")
	}
	if actorId == "" {
		return errors.New("actor id cannot be empty")
	}
	if !lo.Contains(actorIds, actorId) {
		return errors.New("script is not allowed to run for the actor id provided")
	}
	// validation for vendor account id provided is of same actor id provided in request params
	getAccountResp, err := accountManagerClient.GetAccount(ctx, &accountMgPb.GetAccountRequest{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_ALPACA,
		FieldMask: &fieldmaskpb.FieldMask{
			Paths: []string{(&accountMgPb.Account{}).GetAccountVendorAccountIdPath()},
		},
	})
	if err = epifigrpc.RPCError(getAccountResp, err); err != nil {
		return errors.Wrap(err, "failed to get uss account details")
	}
	if getAccountResp.GetAccount().GetVendorAccountId() != vendorAccId {
		return errors.New("vendor account id provided is not of the actor id provided")
	}
	return nil
}

func validateDetailsForCreateBankRelationship() error {
	if bankAccDetails.GetAccountNumber() == "" {
		return errors.New("savings account number cannot be empty")
	}
	if bankAccDetails.GetIfsc() == "" {
		return errors.New("IFSC code cannot be empty")
	}
	if bankAccDetails.GetBankName() == "" {
		return errors.New("bank name cannot be empty")
	}
	if bankAccDetails.GetAccountType() != accountsPb.Type_SAVINGS {
		return errors.New("account type should be savings")
	}
	if bankIdentificationCode == nil || *bankIdentificationCode == "" {
		return errors.New("BIC code cannot be empty")
	}
	if postalAddress.GetPostalCode() == "" {
		return errors.New("postal code cannot be empty")
	}
	if postalAddress.GetAdministrativeArea() == "" {
		return errors.New("administrative area cannot be empty")
	}
	if postalAddress.GetLocality() == "" {
		return errors.New("locality cannot be empty")
	}
	if len(postalAddress.GetAddressLines()) == 0 {
		return errors.New("address lines cannot be empty")
	}
	return nil
}

func validateDetailsForInitiateFundTransfer() error {
	if bankRelationshipId == nil || *bankRelationshipId == "" {
		return errors.New("bank relationship id is nil for initiate fund transfer")
	}
	if amount == nil || *amount == 0 {
		return errors.New("amount is nil for initiate fund transfer")
	}
	return nil
}

func fetchAddressFromInvoices(ctx context.Context, alpacaDb *gorm.DB, invoiceIds string) error {
	var addressCsvRow []*AddressCsvRow
	invoiceIdSlice := strings.Split(strings.ReplaceAll(invoiceIds, " ", ""), ",")

	var walletOrders []*WalletOrder
	if err := alpacaDb.Raw(`SELECT actor_id, gst_invoice_number FROM wallet_orders WHERE gst_invoice_number IN @gst_invoice_numbers`, sql.Named("gst_invoice_numbers", invoiceIdSlice)).Find(&walletOrders).Error; err != nil {
		logger.Error(ctx, "failed to get wallet orders for invoices", zap.Error(err))
		return errors.Wrap(err, "failed to get wallet orders for invoices")
	}

	if len(walletOrders) == 0 {
		logger.Info(ctx, fmt.Sprintf("no wallet orders found for invoices: %v", invoiceIds))
		return nil
	}
	for _, walletOrder := range walletOrders {
		userResp, userErr := userManagerClient.GetUser(ctx, &user.GetUserRequest{Identifier: &user.GetUserRequest_ActorId{
			ActorId: walletOrder.ActorId,
		}})
		if err := epifigrpc.RPCError(userResp, userErr); err != nil {
			if userResp.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "actor id not found in users", zap.String(logger.ACTOR_ID_V2, walletOrder.ActorId), zap.String("invoice_number", walletOrder.GstInvoiceNumber), zap.Error(err))
				continue
			}
			logger.Error(ctx, "failed to get user details", zap.String(logger.ACTOR_ID_V2, walletOrder.ActorId), zap.String("invoice_number", walletOrder.GstInvoiceNumber), zap.Error(err))
			return errors.Wrap(err, "failed to get user details")
		}
		userAddresses := userResp.GetUser().GetProfile().GetAddresses()
		if userAddresses == nil {
			logger.Error(ctx, "address is empty", zap.String(logger.ACTOR_ID_V2, walletOrder.ActorId), zap.String("invoice_number", walletOrder.GstInvoiceNumber))
			continue
		}
		var customerMailingAddress *postaladdress.PostalAddress
		if addr, ok := userAddresses[types.AddressType_MAILING.String()]; ok {
			customerMailingAddress = addr
		}
		if customerMailingAddress == nil {
			logger.Error(ctx, "error getting mailing address from user profile addresses", zap.String(logger.ACTOR_ID_V2, walletOrder.ActorId), zap.String("invoice_number", walletOrder.GstInvoiceNumber))
			continue
		}
		addressCsvRow = append(addressCsvRow, &AddressCsvRow{InvoiceNumber: walletOrder.GstInvoiceNumber, PostalCode: customerMailingAddress.GetPostalCode(), State: customerMailingAddress.GetAdministrativeArea()})
	}
	if addressCsvRow == nil {
		logger.Error(ctx, "no record found to create csv")
		return nil
	}
	csvInBytes, err := fetchFinalCsvBinary(addressCsvRow)
	if err != nil {
		logger.Panic("error fetching final csv in binary", zap.Error(err))
		return err
	}
	// write the csv to s3
	err = writeCsvToS3(fmt.Sprintf("/address_from_invoices/output/address_%d.csv", time.Now().Unix()), csvInBytes)
	if err != nil {
		logger.Error(ctx, "error writing csv to s3", zap.Error(err))
		return err
	}
	return nil
}

func fetchFinalCsvBinary(rows []*AddressCsvRow) ([]byte, error) {
	var finalCsvRows [][]string
	finalCsvRows = append(finalCsvRows, []string{invoiceNumber, state, postalCode})
	for _, row := range rows {
		finalCsvRows = append(finalCsvRows, []string{
			row.InvoiceNumber,
			row.State,
			row.PostalCode,
		})
	}

	buf := new(bytes.Buffer)
	csvWriter := csv.NewWriter(buf)
	if csvWriterError := csvWriter.WriteAll(finalCsvRows); csvWriterError != nil {
		return nil, errors.Wrap(csvWriterError, "read from csvWriter failed")
	}

	return buf.Bytes(), nil
}

// writeCsvToS3 writes a rpc response csv to s3
func writeCsvToS3(filePath string, data []byte) error {
	signedUrl, writeToS3error := s3Client.WriteAndGetPreSignedUrl(filePath, data, 1800)
	if writeToS3error != nil {
		return errors.Wrap(writeToS3error, "write to s3Client failed")
	}
	logger.InfoNoCtx("successfully wrote csv to s3", zap.String("signed_url", signedUrl))
	return nil
}

func deleteBankRelationshipWithVendor(ctx context.Context, vendorAccId, bankRelationshipId string) error {
	req := &vgStocksPb.DeleteBankDetailsRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		BrokerAccountId:    vendorAccId,
		BankRelationshipId: bankRelationshipId,
	}

	logger.Info(ctx, fmt.Sprintf("DeleteBankDetails request: %v", req))
	resp, err := vgStocksClient.DeleteBankDetails(ctx, req)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "failed to delete bank relationship with broker", zap.String("vendor_acc_id", vendorAccId), zap.Error(err))
		return errors.Wrap(err, "failed to delete bank relationship with broker")
	}
	logger.Info(ctx, fmt.Sprintf("DeleteBankDetails response: %v", resp))
	return nil
}

func validateDetailsForDeleteBankRelationship(vendorAccId, bankRelationshipId string) error {
	if vendorAccId == "" {
		return errors.New("vendor account id cannot be empty")
	}
	if bankRelationshipId == "" {
		return errors.New("bank relationship id cannot be empty")
	}
	return nil
}
