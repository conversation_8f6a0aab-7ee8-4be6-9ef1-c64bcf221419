package main

import (
	"context"
	"flag"
	"fmt"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/usstocks"
	catalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usStocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
)

var (
	// hardcoding actorId here so avoid misuse of this script
	actorId = "AC220411tEB+5oC/SvmKuKV5upC9IA=="
	symbol  = flag.String("symbol", "", "symbol for which sell all order to be placed")
)

func main() {
	flag.Parse()
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}

	logger.Init(envName)

	defer func() { _ = logger.Log.Sync() }()

	ctx := context.Background()

	usStocksServiceConn := epifigrpc.NewConnByService(cfg.US_STOCKS_SERVICE)
	defer epifigrpc.CloseConn(usStocksServiceConn)
	catalogManagerClient := catalogPb.NewCatalogManagerClient(usStocksServiceConn)
	orderMangerClient := usStocksOrderPb.NewOrderManagerClient(usStocksServiceConn)

	err = validateRequest()
	if err != nil {
		logger.Error(ctx, "validation failed for sell order", zap.Error(err))
		return
	}

	// get stock id from symbol
	getStocksRes, err := catalogManagerClient.GetStocks(ctx, &catalogPb.GetStocksRequest{
		Identifiers: &catalogPb.GetStocksRequest_Symbols{Symbols: &catalogPb.RepeatedStrings{Ids: []string{*symbol}}},
	})
	if err = epifigrpc.RPCError(getStocksRes, err); err != nil {
		if getStocksRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "stock id not found for symbol", zap.Error(err))
			return
		}
		logger.Error(ctx, "error getting stock id for symbol", zap.Error(err))
		return
	}
	stockDetails, found := getStocksRes.GetStocks()[*symbol]
	if !found {
		logger.Error(ctx, "stock details not found for symbol")
		return
	}

	// place sell all order
	createOrderResp, err := orderMangerClient.CreateOrder(ctx, &usStocksOrderPb.CreateOrderRequest{
		ClientOrderId: uuid.NewString(),
		ActorId:       actorId,
		CatalogRefId:  stockDetails.GetId(),
		Side:          usstocks.OrderSide_SELL,
		Type:          usstocks.OrderType_ORDER_TYPE_SELL_ALL,
		FundingType:   usstocks.OrderFundingType_ORDER_FUNDING_TYPE_WALLET,
		// this amount will not be used for placing sell order
		// sending this so that validation in backend does not fail
		AmountRequested: &money.Money{CurrencyCode: "USD", Units: 1},
		CalculatedTradeAmountAfterChargesDeduction: &money.Money{CurrencyCode: "USD"},
	})
	if err = epifigrpc.RPCError(createOrderResp, err); err != nil {
		logger.Error(ctx, "error placing sell order", zap.Error(err))
		return
	}
	logger.Info(ctx, "sell order placed successfully", zap.Any("create order response", createOrderResp))
}

func validateRequest() error {
	if symbol == nil || *symbol == "" {
		return fmt.Errorf("symbol is required")
	}
	return nil
}
