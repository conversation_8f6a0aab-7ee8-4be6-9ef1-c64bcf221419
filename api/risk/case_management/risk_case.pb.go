// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/risk_case.proto

package case_management

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	enums "github.com/epifi/gamma/api/risk/case_management/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PayloadType int32

const (
	PayloadType_PAYLOAD_TYPE_UNSPECIFIED PayloadType = 0
	// Payload received will be for liveness review
	PayloadType_PAYLOAD_TYPE_LIVENESS PayloadType = 1
	// Payload received will be for full user review
	PayloadType_PAYLOAD_TYPE_USER_REVIEW PayloadType = 2
	// for sample cases generated by ds layer
	PayloadType_PAYLOAD_TYPE_LIVENESS_SAMPLE PayloadType = 3
	// Payload for flagged user transactions
	PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW PayloadType = 19
	// Payload for screener review cases
	PayloadType_PAYLOAD_TYPE_SCREENER PayloadType = 20
)

// Enum value maps for PayloadType.
var (
	PayloadType_name = map[int32]string{
		0:  "PAYLOAD_TYPE_UNSPECIFIED",
		1:  "PAYLOAD_TYPE_LIVENESS",
		2:  "PAYLOAD_TYPE_USER_REVIEW",
		3:  "PAYLOAD_TYPE_LIVENESS_SAMPLE",
		19: "PAYLOAD_TYPE_TRANSACTION_REVIEW",
		20: "PAYLOAD_TYPE_SCREENER",
	}
	PayloadType_value = map[string]int32{
		"PAYLOAD_TYPE_UNSPECIFIED":        0,
		"PAYLOAD_TYPE_LIVENESS":           1,
		"PAYLOAD_TYPE_USER_REVIEW":        2,
		"PAYLOAD_TYPE_LIVENESS_SAMPLE":    3,
		"PAYLOAD_TYPE_TRANSACTION_REVIEW": 19,
		"PAYLOAD_TYPE_SCREENER":           20,
	}
)

func (x PayloadType) Enum() *PayloadType {
	p := new(PayloadType)
	*p = x
	return p
}

func (x PayloadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PayloadType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_case_management_risk_case_proto_enumTypes[0].Descriptor()
}

func (PayloadType) Type() protoreflect.EnumType {
	return &file_api_risk_case_management_risk_case_proto_enumTypes[0]
}

func (x PayloadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PayloadType.Descriptor instead.
func (PayloadType) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_case_management_risk_case_proto_rawDescGZIP(), []int{0}
}

type RiskCase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// we will only support selected payload types
	PayloadType PayloadType `protobuf:"varint,1,opt,name=payload_type,json=payloadType,proto3,enum=risk.case_management.PayloadType" json:"payload_type,omitempty"`
	// not using persistentqueue.Payload directly to avoid direct dependency and have flexibility
	// also only added payload for review types supported currently and relevant to risk cases for now, will expand as needed
	//
	// Types that are assignable to Payload:
	//
	//	*RiskCase_LivenessReview
	//	*RiskCase_UserReview
	//	*RiskCase_TransactionReview
	//	*RiskCase_LivenessSampleReview
	//	*RiskCase_ScreenerReview
	Payload isRiskCase_Payload `protobuf_oneof:"payload"`
	// custom identifier string entered while uploading the risk cases via dev action
	// this will be uised for tracking and monitoring the progress of case ingestion at different steps
	BatchIdentifier string `protobuf:"bytes,6,opt,name=batch_identifier,json=batchIdentifier,proto3" json:"batch_identifier,omitempty"`
	// RuleIdentifier defines the identifier which will be used to query the rules from db
	RuleIdentifier *RuleIdentifier `protobuf:"bytes,7,opt,name=rule_identifier,json=ruleIdentifier,proto3" json:"rule_identifier,omitempty"`
	// File upload timestamp. It is the initiation point for alert created from risk case.
	InitiatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=initiated_at,json=initiatedAt,proto3" json:"initiated_at,omitempty"`
}

func (x *RiskCase) Reset() {
	*x = RiskCase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_risk_case_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCase) ProtoMessage() {}

func (x *RiskCase) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_risk_case_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCase.ProtoReflect.Descriptor instead.
func (*RiskCase) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_risk_case_proto_rawDescGZIP(), []int{0}
}

func (x *RiskCase) GetPayloadType() PayloadType {
	if x != nil {
		return x.PayloadType
	}
	return PayloadType_PAYLOAD_TYPE_UNSPECIFIED
}

func (m *RiskCase) GetPayload() isRiskCase_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (x *RiskCase) GetLivenessReview() *LivenessReview {
	if x, ok := x.GetPayload().(*RiskCase_LivenessReview); ok {
		return x.LivenessReview
	}
	return nil
}

func (x *RiskCase) GetUserReview() *UserReview {
	if x, ok := x.GetPayload().(*RiskCase_UserReview); ok {
		return x.UserReview
	}
	return nil
}

func (x *RiskCase) GetTransactionReview() *TransacionReview {
	if x, ok := x.GetPayload().(*RiskCase_TransactionReview); ok {
		return x.TransactionReview
	}
	return nil
}

func (x *RiskCase) GetLivenessSampleReview() *LivenessSampleReview {
	if x, ok := x.GetPayload().(*RiskCase_LivenessSampleReview); ok {
		return x.LivenessSampleReview
	}
	return nil
}

func (x *RiskCase) GetScreenerReview() *ScreenerReview {
	if x, ok := x.GetPayload().(*RiskCase_ScreenerReview); ok {
		return x.ScreenerReview
	}
	return nil
}

func (x *RiskCase) GetBatchIdentifier() string {
	if x != nil {
		return x.BatchIdentifier
	}
	return ""
}

func (x *RiskCase) GetRuleIdentifier() *RuleIdentifier {
	if x != nil {
		return x.RuleIdentifier
	}
	return nil
}

func (x *RiskCase) GetInitiatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InitiatedAt
	}
	return nil
}

type isRiskCase_Payload interface {
	isRiskCase_Payload()
}

type RiskCase_LivenessReview struct {
	// not using persistentqueue.LivenessReview since we only want to support actor id in request for liveness review cases
	// we wll enrich rest of the fields in consumer before pusing to persistent queue
	LivenessReview *LivenessReview `protobuf:"bytes,2,opt,name=liveness_review,json=livenessReview,proto3,oneof"`
}

type RiskCase_UserReview struct {
	UserReview *UserReview `protobuf:"bytes,3,opt,name=user_review,json=userReview,proto3,oneof"`
}

type RiskCase_TransactionReview struct {
	TransactionReview *TransacionReview `protobuf:"bytes,4,opt,name=transaction_review,json=transactionReview,proto3,oneof"`
}

type RiskCase_LivenessSampleReview struct {
	LivenessSampleReview *LivenessSampleReview `protobuf:"bytes,5,opt,name=liveness_sample_review,json=livenessSampleReview,proto3,oneof"`
}

type RiskCase_ScreenerReview struct {
	ScreenerReview *ScreenerReview `protobuf:"bytes,9,opt,name=screener_review,json=screenerReview,proto3,oneof"`
}

func (*RiskCase_LivenessReview) isRiskCase_Payload() {}

func (*RiskCase_UserReview) isRiskCase_Payload() {}

func (*RiskCase_TransactionReview) isRiskCase_Payload() {}

func (*RiskCase_LivenessSampleReview) isRiskCase_Payload() {}

func (*RiskCase_ScreenerReview) isRiskCase_Payload() {}

// Adding separate message for liveness review payload since we want to only allow actor id in request
// we will enrich other details in consumer before pushing to persistent queue
type LivenessReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *LivenessReview) Reset() {
	*x = LivenessReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_risk_case_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessReview) ProtoMessage() {}

func (x *LivenessReview) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_risk_case_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessReview.ProtoReflect.Descriptor instead.
func (*LivenessReview) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_risk_case_proto_rawDescGZIP(), []int{1}
}

func (x *LivenessReview) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type LivenessSampleReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// request id for the attempt
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Sub sample will act like tags to identify what sort of sampling we are doing (eg. rule based, pre-emptive etc)
	SubSample string `protobuf:"bytes,3,opt,name=sub_sample,json=subSample,proto3" json:"sub_sample,omitempty"`
}

func (x *LivenessSampleReview) Reset() {
	*x = LivenessSampleReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_risk_case_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessSampleReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessSampleReview) ProtoMessage() {}

func (x *LivenessSampleReview) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_risk_case_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessSampleReview.ProtoReflect.Descriptor instead.
func (*LivenessSampleReview) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_risk_case_proto_rawDescGZIP(), []int{2}
}

func (x *LivenessSampleReview) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LivenessSampleReview) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LivenessSampleReview) GetSubSample() string {
	if x != nil {
		return x.SubSample
	}
	return ""
}

type UserReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *UserReview) Reset() {
	*x = UserReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_risk_case_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserReview) ProtoMessage() {}

func (x *UserReview) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_risk_case_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserReview.ProtoReflect.Descriptor instead.
func (*UserReview) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_risk_case_proto_rawDescGZIP(), []int{3}
}

func (x *UserReview) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type TransacionReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Deprecated: Marked as deprecated in api/risk/case_management/risk_case.proto.
	TxnId     string `protobuf:"bytes,2,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	AccountId string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// type of flagged entity
	// EX: Transaction review might be needed if a transaction is flagged(entity type will be transaction in that case and entity id will be internal txn id)
	// It can also be done even if a particular transaction is not flagged but user is flagged as a whole(ex for DS models),
	// in this case transaction review case can be created with entity type as user(entity id will be internal user id).
	EntityType enums.EntityType `protobuf:"varint,4,opt,name=entity_type,json=entityType,proto3,enum=risk.case_management.enums.EntityType" json:"entity_type,omitempty"`
	EntityId   string           `protobuf:"bytes,5,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
}

func (x *TransacionReview) Reset() {
	*x = TransacionReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_risk_case_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransacionReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransacionReview) ProtoMessage() {}

func (x *TransacionReview) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_risk_case_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransacionReview.ProtoReflect.Descriptor instead.
func (*TransacionReview) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_risk_case_proto_rawDescGZIP(), []int{4}
}

func (x *TransacionReview) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/risk/case_management/risk_case.proto.
func (x *TransacionReview) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *TransacionReview) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *TransacionReview) GetEntityType() enums.EntityType {
	if x != nil {
		return x.EntityType
	}
	return enums.EntityType(0)
}

func (x *TransacionReview) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

type ScreenerReview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// screener attempt id for which the case is being created
	ScreenerAttemptId string `protobuf:"bytes,2,opt,name=screener_attempt_id,json=screenerAttemptId,proto3" json:"screener_attempt_id,omitempty"`
}

func (x *ScreenerReview) Reset() {
	*x = ScreenerReview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_risk_case_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenerReview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenerReview) ProtoMessage() {}

func (x *ScreenerReview) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_risk_case_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenerReview.ProtoReflect.Descriptor instead.
func (*ScreenerReview) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_risk_case_proto_rawDescGZIP(), []int{5}
}

func (x *ScreenerReview) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ScreenerReview) GetScreenerAttemptId() string {
	if x != nil {
		return x.ScreenerAttemptId
	}
	return ""
}

var File_api_risk_case_management_risk_case_proto protoreflect.FileDescriptor

var file_api_risk_case_management_risk_case_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc2, 0x05, 0x0a, 0x08,
	0x52, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4f, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x43, 0x0a, 0x0b, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x48, 0x00, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x57,
	0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x48, 0x00, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x62, 0x0a, 0x16, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x14, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x4f, 0x0a, 0x0f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x29, 0x0a, 0x10,
	0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x22, 0x34, 0x0a, 0x0e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x78, 0x0a, 0x14, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x22,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x62, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x22, 0x30, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x22,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x22, 0xdf, 0x01, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x06, 0x74,
	0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x47,
	0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x22, 0x6d, 0x0a, 0x0e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x13, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x49, 0x64, 0x2a, 0xc6, 0x01, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18,
	0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x41,
	0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x53, 0x41, 0x4d, 0x50, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f,
	0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10,
	0x13, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x10, 0x14, 0x42, 0x62, 0x0a, 0x2f,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2f,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5a,
	0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_risk_case_proto_rawDescOnce sync.Once
	file_api_risk_case_management_risk_case_proto_rawDescData = file_api_risk_case_management_risk_case_proto_rawDesc
)

func file_api_risk_case_management_risk_case_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_risk_case_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_risk_case_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_risk_case_proto_rawDescData)
	})
	return file_api_risk_case_management_risk_case_proto_rawDescData
}

var file_api_risk_case_management_risk_case_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_risk_case_management_risk_case_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_risk_case_management_risk_case_proto_goTypes = []interface{}{
	(PayloadType)(0),              // 0: risk.case_management.PayloadType
	(*RiskCase)(nil),              // 1: risk.case_management.RiskCase
	(*LivenessReview)(nil),        // 2: risk.case_management.LivenessReview
	(*LivenessSampleReview)(nil),  // 3: risk.case_management.LivenessSampleReview
	(*UserReview)(nil),            // 4: risk.case_management.UserReview
	(*TransacionReview)(nil),      // 5: risk.case_management.TransacionReview
	(*ScreenerReview)(nil),        // 6: risk.case_management.ScreenerReview
	(*RuleIdentifier)(nil),        // 7: risk.case_management.RuleIdentifier
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
	(enums.EntityType)(0),         // 9: risk.case_management.enums.EntityType
}
var file_api_risk_case_management_risk_case_proto_depIdxs = []int32{
	0, // 0: risk.case_management.RiskCase.payload_type:type_name -> risk.case_management.PayloadType
	2, // 1: risk.case_management.RiskCase.liveness_review:type_name -> risk.case_management.LivenessReview
	4, // 2: risk.case_management.RiskCase.user_review:type_name -> risk.case_management.UserReview
	5, // 3: risk.case_management.RiskCase.transaction_review:type_name -> risk.case_management.TransacionReview
	3, // 4: risk.case_management.RiskCase.liveness_sample_review:type_name -> risk.case_management.LivenessSampleReview
	6, // 5: risk.case_management.RiskCase.screener_review:type_name -> risk.case_management.ScreenerReview
	7, // 6: risk.case_management.RiskCase.rule_identifier:type_name -> risk.case_management.RuleIdentifier
	8, // 7: risk.case_management.RiskCase.initiated_at:type_name -> google.protobuf.Timestamp
	9, // 8: risk.case_management.TransacionReview.entity_type:type_name -> risk.case_management.enums.EntityType
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_risk_case_proto_init() }
func file_api_risk_case_management_risk_case_proto_init() {
	if File_api_risk_case_management_risk_case_proto != nil {
		return
	}
	file_api_risk_case_management_rule_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_risk_case_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskCase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_risk_case_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_risk_case_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessSampleReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_risk_case_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_risk_case_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransacionReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_risk_case_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenerReview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_risk_case_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*RiskCase_LivenessReview)(nil),
		(*RiskCase_UserReview)(nil),
		(*RiskCase_TransactionReview)(nil),
		(*RiskCase_LivenessSampleReview)(nil),
		(*RiskCase_ScreenerReview)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_risk_case_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_risk_case_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_risk_case_proto_depIdxs,
		EnumInfos:         file_api_risk_case_management_risk_case_proto_enumTypes,
		MessageInfos:      file_api_risk_case_management_risk_case_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_risk_case_proto = out.File
	file_api_risk_case_management_risk_case_proto_rawDesc = nil
	file_api_risk_case_management_risk_case_proto_goTypes = nil
	file_api_risk_case_management_risk_case_proto_depIdxs = nil
}
